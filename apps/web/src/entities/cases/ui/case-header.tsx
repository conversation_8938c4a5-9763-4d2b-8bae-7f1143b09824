'use client';

import { CasePriorityBadge } from '@/shared/ui/cases/case-priority-badge';
import { CaseStatusBadge } from '@/shared/ui/cases/case-status-badge';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@lilypad/ui/components/avatar';
import { If } from '@lilypad/ui/components/if';
import { Separator } from '@lilypad/ui/components/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@lilypad/ui/components/tooltip';
import {
  Calendar,
  CircleCheckIcon,
  CircleMinusIcon,
  CircleUserRoundIcon,
  GraduationCap,
  School,
  Users
} from 'lucide-react';
import type { CaseDetailsData } from '../lib/types';
import {
  formatCaseType
} from '../lib/utils';

interface CaseHeaderProps {
  data: CaseDetailsData;
}

export function CaseHeader({ data }: CaseHeaderProps) {
  const { student, school, assignedUsers } = data;

  return (
    <div className="flex h-full items-center gap-6">
      {/* Student Info */}
      <If condition={student}>
        {(studentData) => (
          <div className="flex items-center gap-3">
            <Avatar className="size-8">
              <AvatarFallback className="text-xs">
                <CircleUserRoundIcon className="size-4" />
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium text-sm">{studentData.fullName}</p>
              <div className="flex items-center gap-3 text-muted-foreground text-xs">
                <span className="flex items-center gap-1">
                  <GraduationCap className="size-3" />
                  Grade {studentData.grade}
                </span>
                <If condition={school}>
                  {(schoolData) => (
                    <span className="flex items-center gap-1">
                      <School className="size-3" />
                      {schoolData.name}
                    </span>
                  )}
                </If>
              </div>
            </div>
          </div>
        )}
      </If>

      <Separator className="h-8" orientation="vertical" />

      {/* Case ID and Status */}
      <div className="flex items-center gap-3">
        <div className="flex flex-col items-start gap-1">
          <p className="text-muted-foreground text-xs">Type</p>
          <p className="flex items-center gap-1 text-sm">
            <Tooltip>
              <TooltipTrigger>
                {data.isActive ? (
                  <CircleCheckIcon className="size-3 text-green-500" />
                ) : (
                  <CircleMinusIcon className="size-3 text-red-500" />
                )}
              </TooltipTrigger>
              <TooltipContent>
                {data.isActive ? 'Active' : 'Inactive'}
              </TooltipContent>
            </Tooltip>
            {formatCaseType(data.caseType)}
          </p>
        </div>
        <div className="flex gap-1.5">
          <div className="flex flex-col items-start gap-1">
            <p className="text-muted-foreground text-xs">Status</p>
            <CaseStatusBadge status={data.status} />
          </div>
          <div className="flex flex-col items-start gap-1">
            <p className="text-muted-foreground text-xs">Priority</p>
            <CasePriorityBadge priority={data.priority} />
          </div>
        </div>
      </div>

      <Separator className="h-8" orientation="vertical" />

      {/* Key Dates */}
      <div className="flex items-center gap-4 text-sm">
        <div className="flex flex-col items-start gap-1">
          <p className="flex items-center gap-1 text-muted-foreground text-xs">
            <Calendar className="size-3 text-muted-foreground" />
            Evaluation Due
          </p>
          <p className="font-medium text-xs">
            {data.evaluationDueDate
              ? new Date(data.evaluationDueDate).toLocaleDateString()
              : 'Date not set'}
          </p>
        </div>

        <div className="flex flex-col items-start gap-1">
          <p className="flex items-center gap-1 text-muted-foreground text-xs">
            <Calendar className="size-3 text-muted-foreground" />
            Meeting Date
          </p>
          <p className="font-medium text-xs">
            {data.meetingDate
              ? new Date(data.meetingDate).toLocaleDateString()
              : 'Date not set'}
          </p>
        </div>
      </div>

      <Separator className="h-8" orientation="vertical" />

      {/* Assigned Team Count */}
      <div className="ml-auto flex items-center gap-2">
        <Users className="size-4 text-muted-foreground" />
        <div className="-space-x-2 flex">
          {assignedUsers.slice(0, 3).map((user) => (
            <Avatar className="size-7 border-2 border-background" key={user.id}>
              <AvatarImage src={user.avatar || undefined} />
              <AvatarFallback className="text-xs">
                {user.firstName[0]}
                {user.lastName[0]}
              </AvatarFallback>
            </Avatar>
          ))}
          <If condition={assignedUsers.length > 3}>
            <div className="flex size-7 items-center justify-center rounded-full border-2 border-background bg-muted text-muted-foreground text-xs">
              +{assignedUsers.length - 3}
            </div>
          </If>
        </div>
      </div>
    </div>
  );
}

import { PsychologistAssignmentRepository } from '@lilypad/db/repository/psychologist-assignments';
import { TRPCError } from '@trpc/server';
import { authenticatedProcedure, createTRPCRouter } from '../core/init';
import {
  assignPsychologistsToDistrictSchema,
  getDistrictPsychologistsSchema,
  removePsychologistFromDistrictSchema,
} from '../schemas/psychologist-assignments';

export const psychologistAssignmentsRouter = createTRPCRouter({
  getAvailablePsychologists: authenticatedProcedure.query(async ({ ctx }) => {
    return await ctx.db.transaction(async (tx) => {
      const repository = new PsychologistAssignmentRepository(tx);
      return await repository.getAvailablePsychologists();
    });
  }),

  getDistrictPsychologists: authenticatedProcedure
    .input(getDistrictPsychologistsSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new PsychologistAssignmentRepository(tx);
        return await repository.getDistrictPsychologists(input.districtId);
      });
    }),

  assignPsychologistsToDistrict: authenticatedProcedure
    .input(assignPsychologistsToDistrictSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        return await ctx.db.transaction(async (tx) => {
          const repository = new PsychologistAssignmentRepository(tx);
          return await repository.assignPsychologistsToDistrict(
            input.districtId,
            input.psychologistIds
          );
        });
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to assign psychologists to district',
          cause: error,
        });
      }
    }),

  removePsychologistFromDistrict: authenticatedProcedure
    .input(removePsychologistFromDistrictSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        return await ctx.db.transaction(async (tx) => {
          const repository = new PsychologistAssignmentRepository(tx);
          return await repository.removePsychologistFromDistrict(
            input.districtId,
            input.psychologistId
          );
        });
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to remove psychologist from district',
          cause: error,
        });
      }
    }),
});

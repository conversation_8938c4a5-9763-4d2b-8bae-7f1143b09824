import { z } from 'zod';

export const assignPsychologistsToDistrictSchema = z.object({
  districtId: z.string().uuid(),
  psychologistIds: z.array(z.string().uuid()),
});

export const removePsychologistFromDistrictSchema = z.object({
  districtId: z.string().uuid(),
  psychologistId: z.string().uuid(),
});

export const getDistrictPsychologistsSchema = z.object({
  districtId: z.string().uuid(),
});

import { and, eq, inArray, isNull, ne, or } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { RoleEnum } from '../schema/enums';
import {
  casesTable,
  caseAssignmentsTable,
  userDistrictsTable,
  userRolesTable,
  usersTable,
} from '../schema/tables';

export class PsychologistAssignmentRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  /**
   * Get all psychologists available for assignment
   */
  async getAvailablePsychologists() {
    return await this.tx
      .select({
        id: usersTable.id,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        fullName: usersTable.fullName,
        email: usersTable.email,
        avatar: usersTable.avatar,
      })
      .from(usersTable)
      .innerJoin(userRolesTable, eq(usersTable.id, userRolesTable.userId))
      .where(
        and(
          eq(userRolesTable.roleName, RoleEnum.PSYCHOLOGIST),
          isNull(usersTable.deletedAt)
        )
      );
  }

  /**
   * Get psychologists assigned to a specific district
   */
  async getDistrictPsychologists(districtId: string) {
    return await this.tx
      .select({
        id: usersTable.id,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        fullName: usersTable.fullName,
        email: usersTable.email,
        avatar: usersTable.avatar,
      })
      .from(usersTable)
      .innerJoin(
        userDistrictsTable,
        eq(usersTable.id, userDistrictsTable.userId)
      )
      .innerJoin(userRolesTable, eq(usersTable.id, userRolesTable.userId))
      .where(
        and(
          eq(userDistrictsTable.districtId, districtId),
          eq(userRolesTable.roleName, RoleEnum.PSYCHOLOGIST),
          isNull(usersTable.deletedAt)
        )
      );
  }

  /**
   * Assign psychologists to a district
   */
  async assignPsychologistsToDistrict(
    districtId: string,
    psychologistIds: string[]
  ) {
    if (psychologistIds.length === 0) {
      return [];
    }

    const assignments = psychologistIds.map((psychologistId) => ({
      districtId,
      userId: psychologistId,
    }));

    return await this.tx
      .insert(userDistrictsTable)
      .values(assignments)
      .onConflictDoNothing()
      .returning();
  }

  /**
   * Remove psychologist from district
   */
  async removePsychologistFromDistrict(
    districtId: string,
    psychologistId: string
  ) {
    return await this.tx
      .delete(userDistrictsTable)
      .where(
        and(
          eq(userDistrictsTable.districtId, districtId),
          eq(userDistrictsTable.userId, psychologistId)
        )
      )
      .returning();
  }

  /**
   * Get psychologists assigned to a specific case
   */
  async getCasePsychologists(caseId: string) {
    const [primaryPsychologist] = await this.tx
      .select({
        id: usersTable.id,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        fullName: usersTable.fullName,
        email: usersTable.email,
        avatar: usersTable.avatar,
        isPrimary: true,
      })
      .from(casesTable)
      .innerJoin(
        usersTable,
        eq(casesTable.primaryPsychologistId, usersTable.id)
      )
      .where(eq(casesTable.id, caseId))
      .limit(1);

    const secondaryPsychologists = await this.tx
      .select({
        id: usersTable.id,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        fullName: usersTable.fullName,
        email: usersTable.email,
        avatar: usersTable.avatar,
        isPrimary: false,
      })
      .from(caseAssignmentsTable)
      .innerJoin(usersTable, eq(caseAssignmentsTable.userId, usersTable.id))
      .innerJoin(userRolesTable, eq(usersTable.id, userRolesTable.userId))
      .where(
        and(
          eq(caseAssignmentsTable.caseId, caseId),
          eq(userRolesTable.roleName, RoleEnum.PSYCHOLOGIST),
          eq(caseAssignmentsTable.isDeleted, false),
          isNull(usersTable.deletedAt)
        )
      );

    return [
      ...(primaryPsychologist ? [primaryPsychologist] : []),
      ...secondaryPsychologists,
    ];
  }

  /**
   * Set primary psychologist for a case
   */
  async setPrimaryPsychologist(caseId: string, psychologistId: string) {
    return await this.tx
      .update(casesTable)
      .set({ primaryPsychologistId: psychologistId })
      .where(eq(casesTable.id, caseId))
      .returning();
  }

  /**
   * Remove primary psychologist from a case
   */
  async removePrimaryPsychologist(caseId: string) {
    return await this.tx
      .update(casesTable)
      .set({ primaryPsychologistId: null })
      .where(eq(casesTable.id, caseId))
      .returning();
  }

  /**
   * Assign secondary psychologists to a case
   */
  async assignSecondaryPsychologists(
    caseId: string,
    psychologistIds: string[]
  ) {
    if (psychologistIds.length === 0) {
      return [];
    }

    const assignments = psychologistIds.map((psychologistId) => ({
      caseId,
      userId: psychologistId,
    }));

    return await this.tx
      .insert(caseAssignmentsTable)
      .values(assignments)
      .onConflictDoNothing()
      .returning();
  }

  /**
   * Remove secondary psychologist from case
   */
  async removeSecondaryPsychologist(caseId: string, psychologistId: string) {
    return await this.tx
      .update(caseAssignmentsTable)
      .set({ isDeleted: true, deletedAt: new Date() })
      .where(
        and(
          eq(caseAssignmentsTable.caseId, caseId),
          eq(caseAssignmentsTable.userId, psychologistId),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .returning();
  }

  /**
   * Get all cases where a psychologist is the primary psychologist
   */
  async getPrimaryCases(psychologistId: string) {
    return await this.tx
      .select({
        id: casesTable.id,
        displayId: casesTable.displayId,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        isActive: casesTable.isActive,
      })
      .from(casesTable)
      .where(
        and(
          eq(casesTable.primaryPsychologistId, psychologistId),
          eq(casesTable.isDeleted, false)
        )
      );
  }

  /**
   * Get all cases where a psychologist is assigned (primary or secondary)
   */
  async getAllAssignedCases(psychologistId: string) {
    const primaryCases = await this.getPrimaryCases(psychologistId);

    const secondaryCases = await this.tx
      .select({
        id: casesTable.id,
        displayId: casesTable.displayId,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        isActive: casesTable.isActive,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        eq(casesTable.id, caseAssignmentsTable.caseId)
      )
      .where(
        and(
          eq(caseAssignmentsTable.userId, psychologistId),
          eq(caseAssignmentsTable.isDeleted, false),
          eq(casesTable.isDeleted, false),
          or(
            isNull(casesTable.primaryPsychologistId),
            ne(casesTable.primaryPsychologistId, psychologistId)
          )
        )
      );

    return [...primaryCases, ...secondaryCases];
  }
}

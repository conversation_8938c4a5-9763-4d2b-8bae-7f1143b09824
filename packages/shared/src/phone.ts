import {
  isValidPhoneNumber,
  parsePhoneNumberFromString,
} from 'libphonenumber-js';

const FORMAT_6 = /(\d{3})(\d{3})/;
const FORMAT_10 = /(\d{3})(\d{3})(\d{4})/;

export function formatUSPhone(input = ''): string {
  if (!input) {
    return '';
  }
  const numericInput = input.replace(/\D/g, '');
  if (!numericInput) {
    return '';
  }
  const cleanInput = numericInput.slice(0, 10);
  if (cleanInput.length <= 3) {
    return cleanInput;
  }
  if (cleanInput.length <= 6) {
    return cleanInput.replace(FORMAT_6, '($1) $2');
  }
  return cleanInput.replace(FORMAT_10, '($1) $2-$3');
}

const FORMAT_PHONE = /^\+?1?(\d{3})(\d{3})(\d{4})$/;
export function displayPhoneNumber(phoneNumber?: string): string {
  if (!phoneNumber) {
    return '';
  }
  try {
    const parsed = parsePhoneNumberFromString(phoneNumber, 'US');
    if (parsed?.isValid()) {
      return parsed.formatNational();
    }
  } catch {
    //
  }

  return phoneNumber.replace(FORMAT_PHONE, '($1) $2-$3');
}

export function validatePhone(phoneNumber = ''): boolean {
  if (!phoneNumber) {
    return false;
  }
  if (isValidPhoneNumber(phoneNumber, 'US')) {
    return true;
  }
  return isValidPhoneNumber(phoneNumber);
}

/**
 * Normalizes US phone numbers to E.164 format (+1XXXXXXXXXX)
 * Handles various input formats and returns consistent output
 */
export function normalizePhone(phoneNumber = ''): string {
  if (!phoneNumber) {
    return '';
  }

  try {
    let parsed = parsePhoneNumberFromString(phoneNumber, 'US');
    if (!parsed?.isValid()) {
      parsed = parsePhoneNumberFromString(phoneNumber);
    }
    if (parsed?.isValid() && parsed.country === 'US') {
      return parsed.format('E.164');
    }
  } catch {
    //
  }

  return phoneNumber;
}

CREATE TYPE "public"."address_type" AS ENUM('PHY<PERSON>CAL', 'POS<PERSON><PERSON>', 'BILLING', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."admin_status" AS ENUM('PLANNED', 'IN_PROGRESS', 'COMPLETED', 'DISCONTINUED', 'INVALID', 'PARTIAL');--> statement-breakpoint
CREATE TYPE "public"."attendee_status" AS ENUM('PENDING', 'ACCEPTED', 'DECLINED', 'TENTATIVE');--> statement-breakpoint
CREATE TYPE "public"."availability_type" AS ENUM('EVALUATION', 'MEETING', 'CONSULTATION', 'ALL');--> statement-breakpoint
CREATE TYPE "public"."blocked_date_type" AS ENUM('HOLIDAY', 'STAFF_TRAINING', 'DISTRICT_CLOSURE', 'MAINTENANCE', 'SPECIAL_EVENT', 'VACATION', 'WEATHER', 'EMERGENCY', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."case_priority" AS ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT');--> statement-breakpoint
CREATE TYPE "public"."case_status" AS ENUM('READY_FOR_EVALUATION', 'EVALUATION_IN_PROGRESS', 'REPORT_IN_PROGRESS', 'AWAITING_MEETING', 'MEETING_COMPLETE');--> statement-breakpoint
CREATE TYPE "public"."case_type" AS ENUM('INITIAL_EVALUATION', 'TRIENNIAL_EVALUATION', 'REEVALUATION', 'INDEPENDENT_EVALUATION');--> statement-breakpoint
CREATE TYPE "public"."case_workflow_status" AS ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'SKIPPED');--> statement-breakpoint
CREATE TYPE "public"."day_of_week" AS ENUM('SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY');--> statement-breakpoint
CREATE TYPE "public"."district_type" AS ENUM('ELEMENTARY_DISTRICT', 'SECONDARY_DISTRICT', 'UNIFIED_DISTRICT', 'SUPERVISORY_UNION_ADMIN', 'REGIONAL_SERVICE_AGENCY', 'STATE_OPERATED_AGENCY', 'FEDERAL_OPERATED_AGENCY', 'CHARTER_LEA', 'OTHER_EDUCATION_AGENCY', 'SPECIALIZED_PUBLIC_DISTRICT');--> statement-breakpoint
CREATE TYPE "public"."document_category" AS ENUM('ASSESSMENT', 'BACKGROUND', 'CONSENT_FORM', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."enrollment_status" AS ENUM('ENROLLED', 'WITHDRAWN', 'TRANSFERRED', 'GRADUATED', 'SUSPENDED', 'EXPELLED', 'INACTIVE');--> statement-breakpoint
CREATE TYPE "public"."event_status" AS ENUM('SCHEDULED', 'CANCELLED', 'COMPLETED', 'RESCHEDULED', 'NO_SHOW');--> statement-breakpoint
CREATE TYPE "public"."event_type" AS ENUM('EVALUATION', 'IEP_MEETING', 'OBSERVATION_MEETING', 'GENERAL_MEETING');--> statement-breakpoint
CREATE TYPE "public"."issue_type" AS ENUM('VISUAL', 'FUNCTIONALITY', 'PERFORMANCE', 'SECURITY', 'DATA', 'USABILITY', 'ACCESSIBILITY', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."status" AS ENUM('OPEN', 'IN_PROGRESS', 'RESOLVED', 'RELEASED', 'REJECTED');--> statement-breakpoint
CREATE TYPE "public"."feedback_type" AS ENUM('BUG', 'FEATURE_REQUEST', 'GENERAL');--> statement-breakpoint
CREATE TYPE "public"."gender" AS ENUM('MALE', 'FEMALE', 'NON_BINARY', 'PREFER_NOT_TO_SAY', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."iep_status" AS ENUM('ACTIVE', 'INACTIVE');--> statement-breakpoint
CREATE TYPE "public"."index_type" AS ENUM('PRIMARY', 'COMPOSITE', 'SUPPLEMENTAL', 'PROCESS_SPECIFIC', 'ANCILLARY');--> statement-breakpoint
CREATE TYPE "public"."invitation_status" AS ENUM('PENDING', 'ACCEPTED', 'REJECTED', 'EXPIRED');--> statement-breakpoint
CREATE TYPE "public"."notification_category_type" AS ENUM('GENERAL', 'TASKS', 'EVALUATIONS', 'REPORTS', 'MEETINGS');--> statement-breakpoint
CREATE TYPE "public"."notification_channel" AS ENUM('EMAIL', 'IN_APP');--> statement-breakpoint
CREATE TYPE "public"."notification_type" AS ENUM('GENERAL', 'SYSTEM_UPDATE', 'REMINDER', 'TASK_ASSIGNED', 'TASK_ACCEPTED', 'TASK_REJECTED', 'EVALUATION_SCHEDULED', 'RATING_SCALES_REMINDER', 'UPCOMING_EVALUATION_REMINDER', 'MEETING_SCHEDULED', 'MEETING_DEADLINE_APPROACHING', 'APPROVAL_NEEDED', 'REPORT_READY');--> statement-breakpoint
CREATE TYPE "public"."parent_relationship" AS ENUM('MOTHER', 'FATHER', 'STEP_MOTHER', 'STEP_FATHER', 'GUARDIAN', 'ADOPTIVE_MOTHER', 'ADOPTIVE_FATHER', 'GRANDMOTHER', 'GRANDFATHER', 'AUNT', 'UNCLE', 'FOSTER_MOTHER', 'FOSTER_FATHER', 'OTHER_RELATIVE', 'NON_RELATIVE', 'UNKNOWN');--> statement-breakpoint
CREATE TYPE "public"."plan_status" AS ENUM('PENDING', 'ACTIVE', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."plan_type" AS ENUM('IEP', '504', 'BIP', 'SST');--> statement-breakpoint
CREATE TYPE "public"."preference_category" AS ENUM('NOTIFICATIONS', 'USER_MANAGEMENT', 'WORKFLOW', 'REPORTING', 'INTEGRATIONS', 'EVALUATION', 'SECURITY');--> statement-breakpoint
CREATE TYPE "public"."preference_type" AS ENUM('BOOLEAN', 'STRING', 'NUMBER', 'JSON');--> statement-breakpoint
CREATE TYPE "public"."role" AS ENUM('SUPER_USER', 'SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'SCHOOL_ADMIN', 'PROCTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT');--> statement-breakpoint
CREATE TYPE "public"."school_type" AS ENUM('REGULAR_PUBLIC_PRIMARY', 'REGULAR_PUBLIC_MIDDLE', 'REGULAR_PUBLIC_HIGH', 'REGULAR_PUBLIC_UNIFIED', 'SPECIAL_ED_PUBLIC', 'VOCATIONAL_PUBLIC', 'ALTERNATIVE_PUBLIC', 'REPORTABLE_PROGRAM', 'PUBLIC_CHARTER', 'MAGNET_PUBLIC', 'VIRTUAL_PUBLIC', 'DODEA_SCHOOL', 'BIE_SCHOOL', 'PRIVATE_CATHOLIC', 'PRIVATE_OTHER_RELIGIOUS', 'PRIVATE_NONSECTARIAN');--> statement-breakpoint
CREATE TYPE "public"."session_status" AS ENUM('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'RESCHEDULED', 'INCOMPLETE');--> statement-breakpoint
CREATE TYPE "public"."session_type" AS ENUM('INITIAL_EVALUATION', 'TRIENNIAL_EVALUATION', 'REEVALUATION', 'INDEPENDENT_EVALUATION', 'PROGRESS_MONITORING', 'DIAGNOSTIC_CLARIFICATION', 'COMPREHENSIVE_EVALUATION');--> statement-breakpoint
CREATE TYPE "public"."subtest_type" AS ENUM('CORE', 'SUPPLEMENTAL', 'PROCESS_SPECIFIC', 'OPTIONAL', 'ANCILLARY', 'RATING_SCALE');--> statement-breakpoint
CREATE TYPE "public"."task_history_action" AS ENUM('CREATED', 'ASSIGNED', 'REASSIGNED', 'STATUS_CHANGED', 'PRIORITY_CHANGED', 'DUE_DATE_CHANGED', 'COMPLETED', 'CANCELLED', 'NOTES_ADDED', 'REOPENED', 'REJECTED');--> statement-breakpoint
CREATE TYPE "public"."task_priority" AS ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT');--> statement-breakpoint
CREATE TYPE "public"."task_status" AS ENUM('TODO', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'REJECTED');--> statement-breakpoint
CREATE TYPE "public"."task_type" AS ENUM('ASSIGN_PSYCHOLOGIST', 'REASSIGN_PSYCHOLOGIST', 'REVIEW_DISTRICT_ASSIGNMENT', 'SHIP_EVALUATION_MATERIALS', 'UPDATE_AVAILABILITY', 'COMPLETE_REFERRAL_FORM', 'SCHEDULE_STUDENT_EVALUATIONS', 'GENERATE_CALENDAR_INVITES', 'CREATE_EVALUATION_PLAN', 'PREPARE_RATING_SCALES', 'REVIEW_AND_SEND_RATING_SCALES', 'MONITOR_RATING_SCALES', 'PREPARE_ASSESSMENT_MATERIALS', 'PREPARE_FOR_EVALUATION', 'JOIN_EVALUATION_AS_PROCTOR', 'JOIN_EVALUATION_AS_PSYCHOLOGIST', 'MARK_EVALUATION_COMPLETE', 'COMPLETE_STUDENT_INTERVIEW', 'UPLOAD_PROTOCOLS', 'UPDATE_ASSESSMENT_SCORES', 'GENERATE_REPORT_DRAFT', 'FINALIZE_EVALUATION_REPORT', 'SCORE_REPORT_QUALITY', 'REVIEW_FINAL_REPORT', 'MARK_REPORT_RECEIVED', 'SCHEDULE_IEP_MEETING', 'PREPARE_FOR_IEP_MEETING', 'SEND_MEETING_INVITATIONS', 'COMPLETE_IEP_MEETING');--> statement-breakpoint
CREATE TYPE "public"."test_category" AS ENUM('COGNITIVE_ASSESSMENT', 'ACADEMIC_ACHIEVEMENT', 'SOCIAL_EMOTIONAL_ASSESSMENT', 'NEUROPSYCHOLOGICAL_ASSESSMENT', 'ADAPTIVE_BEHAVIOR');--> statement-breakpoint
CREATE TABLE "assessment_sessions" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"display_id" varchar(20) DEFAULT generate_display_id('sess') NOT NULL,
	"student_id" uuid NOT NULL,
	"case_id" uuid,
	"psychologist_id" uuid NOT NULL,
	"session_date" timestamp with time zone NOT NULL,
	"session_duration" integer,
	"session_type" "session_type" NOT NULL,
	"location" varchar(255),
	"referral_reason" text,
	"background_information" text,
	"behavioral_observations" text,
	"testing_conditions" text,
	"accommodations_provided" jsonb,
	"environmental_factors" text,
	"session_status" "session_status" DEFAULT 'SCHEDULED',
	"validity_concerns" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "assessment_sessions_displayId_unique" UNIQUE("display_id")
);
--> statement-breakpoint
ALTER TABLE "assessment_sessions" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "index_scores" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"administration_id" uuid NOT NULL,
	"index_id" uuid NOT NULL,
	"composite_score" integer,
	"percentile_rank" numeric(5, 2),
	"confidence_interval_lower" integer,
	"confidence_interval_upper" integer,
	"confidence_level" integer DEFAULT 95,
	"descriptive_category" varchar(100),
	"qualitative_descriptor" varchar(100),
	"calculation_method" varchar(100),
	"component_subtests" jsonb,
	"interpretation_notes" text,
	"strengths_identified" text,
	"weaknesses_identified" text,
	"is_valid" boolean DEFAULT true,
	"validity_notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "index_scores" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "index_subtest_mappings" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"index_id" uuid NOT NULL,
	"subtest_id" uuid NOT NULL,
	"weight" numeric(3, 2) DEFAULT '1.00',
	"is_required" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "index_subtest_mappings" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "subtest_scores" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"administration_id" uuid NOT NULL,
	"subtest_id" uuid NOT NULL,
	"raw_score" integer,
	"scaled_score" integer,
	"percentile_rank" numeric(5, 2),
	"confidence_interval_lower" integer,
	"confidence_interval_upper" integer,
	"confidence_level" integer DEFAULT 95,
	"age_equivalent" varchar(20),
	"grade_equivalent" varchar(20),
	"completion_time_minutes" integer,
	"discontinued_item" integer,
	"descriptive_category" varchar(100),
	"qualitative_descriptor" varchar(100),
	"strengths_identified" text,
	"weaknesses_identified" text,
	"scoring_notes" text,
	"is_valid" boolean DEFAULT true,
	"validity_notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "subtest_scores" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "subtests" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"battery_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"code" varchar(20) NOT NULL,
	"subtest_type" "subtest_type" NOT NULL,
	"description" text,
	"measured_abilities" jsonb,
	"time_limit_minutes" integer,
	"score_range_min" integer DEFAULT 1,
	"score_range_max" integer DEFAULT 19,
	"mean_score" integer DEFAULT 10,
	"standard_deviation" integer DEFAULT 3,
	"administration_instructions" text,
	"sort_order" integer DEFAULT 0,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "subtests" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "test_administrations" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"session_id" uuid NOT NULL,
	"battery_id" uuid NOT NULL,
	"administration_order" integer NOT NULL,
	"start_time" timestamp with time zone,
	"end_time" timestamp with time zone,
	"administration_notes" text,
	"accommodations_used" jsonb,
	"discontinuation_notes" text,
	"validity_indicators" jsonb,
	"admin_status" "admin_status" DEFAULT 'PLANNED',
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "test_administrations" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "test_batteries" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"category" "test_category" NOT NULL,
	"name" varchar(255) NOT NULL,
	"code" varchar(20) NOT NULL,
	"version" varchar(20),
	"publisher" varchar(255),
	"age_range_min" integer,
	"age_range_max" integer,
	"administration_time" integer,
	"description" text,
	"norming_information" jsonb,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "test_batteries_code_unique" UNIQUE("code")
);
--> statement-breakpoint
ALTER TABLE "test_batteries" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "test_indices" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"battery_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"code" varchar(20) NOT NULL,
	"index_type" "index_type" NOT NULL,
	"description" text,
	"score_range_min" integer DEFAULT 40,
	"score_range_max" integer DEFAULT 160,
	"mean_score" integer DEFAULT 100,
	"standard_deviation" integer DEFAULT 15,
	"interpretive_guidelines" text,
	"sort_order" integer DEFAULT 0,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "test_indices" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "addresses" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"type" "address_type" DEFAULT 'PHYSICAL' NOT NULL,
	"address" varchar(255) NOT NULL,
	"address2" varchar(255),
	"city" varchar(255) NOT NULL,
	"state" varchar(255) NOT NULL,
	"zipcode" varchar(255) NOT NULL,
	"student_id" uuid,
	"district_id" uuid,
	"school_id" uuid,
	"parent_id" uuid,
	"is_primary" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "addresses" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "availabilities" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"user_id" uuid NOT NULL,
	"day" "day_of_week" NOT NULL,
	"start_time" timestamp with time zone NOT NULL,
	"end_time" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "availabilities" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "case_assignments" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"user_id" uuid NOT NULL,
	"case_id" uuid NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp with time zone,
	"deleted_by" uuid,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "case_assignments" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "case_details" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"case_id" uuid NOT NULL,
	"key" varchar(255) NOT NULL,
	"value" text NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp with time zone,
	"deleted_by" uuid,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "case_workflow_step_statuses" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"case_workflow_id" uuid NOT NULL,
	"step_id" uuid NOT NULL,
	"status" "case_workflow_status" DEFAULT 'PENDING' NOT NULL,
	"started_at" timestamp with time zone,
	"completed_at" timestamp with time zone,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "case_workflows" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"case_id" uuid NOT NULL,
	"workflow_id" uuid NOT NULL,
	"status" varchar(50) DEFAULT 'ACTIVE' NOT NULL,
	"started_at" timestamp with time zone DEFAULT now() NOT NULL,
	"completed_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "cases" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"display_id" varchar(20) DEFAULT generate_display_id('case') NOT NULL,
	"status" "case_status" NOT NULL,
	"priority" "case_priority" DEFAULT 'MEDIUM' NOT NULL,
	"case_type" "case_type" NOT NULL,
	"student_id" uuid NOT NULL,
	"is_active" boolean NOT NULL,
	"iep_status" "iep_status" NOT NULL,
	"iep_start_date" timestamp with time zone NOT NULL,
	"iep_end_date" timestamp with time zone NOT NULL,
	"date_of_consent" timestamp with time zone NOT NULL,
	"evaluation_due_date" timestamp with time zone,
	"meeting_date" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp with time zone,
	"deleted_by" uuid,
	CONSTRAINT "cases_displayId_unique" UNIQUE("display_id")
);
--> statement-breakpoint
ALTER TABLE "cases" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "district_availabilities" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"district_id" uuid NOT NULL,
	"type" "availability_type" DEFAULT 'EVALUATION' NOT NULL,
	"day" "day_of_week" NOT NULL,
	"start_time" timestamp NOT NULL,
	"end_time" timestamp NOT NULL,
	"time_zone" varchar(255) NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"notes" text,
	"created_by" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "district_availabilities" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "district_blocked_dates" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"district_id" uuid NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"block_type" "blocked_date_type" DEFAULT 'OTHER' NOT NULL,
	"start_date" date NOT NULL,
	"end_date" date NOT NULL,
	"is_recurring" boolean DEFAULT false NOT NULL,
	"recurrence_pattern" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"affects_types" text[],
	"created_by" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "district_blocked_dates" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "district_preferences" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"district_id" uuid NOT NULL,
	"category" "preference_category" NOT NULL,
	"key" varchar(255) NOT NULL,
	"type" "preference_type" NOT NULL,
	"value" text NOT NULL,
	"last_modified_by" uuid,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "district_preferences" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "districts" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"name" varchar(255) NOT NULL,
	"slug" varchar(255) NOT NULL,
	"logo" text,
	"type" "district_type" DEFAULT 'UNIFIED_DISTRICT' NOT NULL,
	"website" varchar(255) NOT NULL,
	"nces_id" varchar(255) NOT NULL,
	"state_id" varchar(255) NOT NULL,
	"county" varchar(255) NOT NULL,
	"num_schools" integer,
	"num_students" integer,
	"invoice_email" varchar(255) NOT NULL,
	"address_id" uuid NOT NULL
);
--> statement-breakpoint
ALTER TABLE "districts" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "documents" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"student_id" uuid NOT NULL,
	"assessment_session_id" uuid,
	"test_administration_id" uuid,
	"category" "document_category" NOT NULL,
	"name" varchar(255) NOT NULL,
	"url" text NOT NULL,
	"uploaded_user_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "documents" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "feedback_files" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"feedback_id" uuid NOT NULL,
	"file_url" varchar NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "feedback_files" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "feedback" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"user_id" uuid NOT NULL,
	"rating" integer,
	"type" "feedback_type" NOT NULL,
	"status" "status" DEFAULT 'OPEN' NOT NULL,
	"title" varchar,
	"description" text,
	"issue_type" "issue_type" DEFAULT 'OTHER' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "feedback" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "invitation_schools" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"invitation_id" uuid NOT NULL,
	"school_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "invitation_schools" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "invitations" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"role_id" uuid NOT NULL,
	"district_id" uuid NOT NULL,
	"inviter_id" uuid NOT NULL,
	"first_name" varchar(255) NOT NULL,
	"last_name" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"status" "invitation_status" DEFAULT 'PENDING' NOT NULL,
	"token" varchar NOT NULL,
	"expires_at" timestamp with time zone DEFAULT (NOW() + INTERVAL '7 days') NOT NULL,
	"accepted_at" timestamp with time zone,
	"rejected_at" timestamp with time zone,
	"invited_by_id" uuid,
	"metadata" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "invitations" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "join_requests" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"first_name" varchar(255) NOT NULL,
	"last_name" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"phone" varchar(255) NOT NULL,
	"district_name" varchar(255) NOT NULL,
	"message" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "join_requests" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "languages" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"name" varchar(25) NOT NULL,
	"code" varchar(10) NOT NULL,
	"emoji" varchar(10) NOT NULL,
	CONSTRAINT "languages_code_unique" UNIQUE("code")
);
--> statement-breakpoint
ALTER TABLE "languages" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "notifications" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"user_id" uuid NOT NULL,
	"type" "notification_type" NOT NULL,
	"content" text NOT NULL,
	"metadata" jsonb,
	"is_read" boolean DEFAULT false NOT NULL,
	"is_archived" boolean DEFAULT false NOT NULL,
	"category" "notification_category_type" DEFAULT 'GENERAL' NOT NULL,
	"read_at" timestamp with time zone,
	"archived_at" timestamp with time zone,
	"expires_at" timestamp with time zone DEFAULT now() + interval '1 month',
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "notifications" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "parents" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"first_name" varchar(255) NOT NULL,
	"middle_name" varchar(255),
	"last_name" varchar(255) NOT NULL,
	"full_name" varchar(255) GENERATED ALWAYS AS (concat_names("parents"."first_name", "parents"."middle_name", "parents"."last_name")) STORED NOT NULL,
	"primary_email" varchar(255),
	"secondary_email" varchar(255),
	"primary_phone" varchar(255),
	"secondary_phone" varchar(255),
	"relationship_type" "parent_relationship" DEFAULT 'UNKNOWN' NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp with time zone,
	"deleted_by" uuid,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "parents" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "permissions" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"name" varchar(255) NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "permissions" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "plans" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"display_id" varchar(20) DEFAULT generate_display_id('plan') NOT NULL,
	"student_id" uuid NOT NULL,
	"case_id" uuid NOT NULL,
	"type" "plan_type" NOT NULL,
	"status" "plan_status" NOT NULL,
	"expiration_date" timestamp with time zone NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp with time zone,
	"deleted_by" uuid,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "plans_displayId_unique" UNIQUE("display_id")
);
--> statement-breakpoint
ALTER TABLE "plans" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "role_permissions" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"role_id" uuid NOT NULL,
	"permission_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "role_permissions" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "roles" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"name" "role" NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "roles" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "schools" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"name" varchar(255) NOT NULL,
	"slug" varchar(255) NOT NULL,
	"type" "school_type" NOT NULL,
	"website" varchar(255),
	"nces_id" varchar(255) NOT NULL,
	"district_id" uuid NOT NULL,
	"address_id" uuid NOT NULL
);
--> statement-breakpoint
ALTER TABLE "schools" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "workflow_step_dependencies" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"step_id" uuid NOT NULL,
	"depends_on_step_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "student_enrollments" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"student_id" uuid NOT NULL,
	"school_id" uuid NOT NULL,
	"start_date" date DEFAULT now(),
	"end_date" date,
	"district_id" uuid,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "student_enrollments" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "student_languages" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"is_primary" boolean DEFAULT false NOT NULL,
	"student_id" uuid NOT NULL,
	"language_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "student_languages" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "student_parents" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"parent_id" uuid NOT NULL,
	"student_id" uuid NOT NULL,
	"is_primary_contact" boolean DEFAULT false NOT NULL,
	"has_pickup_authorization" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "student_parents" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "students" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"first_name" varchar(255) NOT NULL,
	"middle_name" varchar(255),
	"last_name" varchar(255) NOT NULL,
	"full_name" varchar(255) GENERATED ALWAYS AS (concat_names("students"."first_name", "students"."middle_name", "students"."last_name")) STORED,
	"preferred_name" varchar(255),
	"student_id_number" varchar(50) NOT NULL,
	"date_of_birth" date NOT NULL,
	"grade" varchar(2) NOT NULL,
	"gender" "gender" NOT NULL,
	"primary_school_id" uuid,
	"private_school" varchar(255),
	"enrollment_status" "enrollment_status" DEFAULT 'ENROLLED' NOT NULL,
	"emergency_contact_name" varchar(255),
	"emergency_contact_phone" varchar(255),
	"is_deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp with time zone,
	"deleted_by" uuid,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "students" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "task_dependencies" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"predecessor_task_id" uuid NOT NULL,
	"successor_task_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "task_history" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"task_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"action" "task_history_action" NOT NULL,
	"previous_status" "task_status",
	"new_status" "task_status",
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tasks" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"task_type" "task_type" NOT NULL,
	"case_id" uuid,
	"student_id" uuid,
	"school_id" uuid,
	"district_id" uuid,
	"workflow_step_id" uuid,
	"workflow_step_task_id" uuid,
	"assigned_to_id" uuid NOT NULL,
	"assigned_by_id" uuid DEFAULT null,
	"status" "task_status" DEFAULT 'TODO' NOT NULL,
	"priority" "task_priority" DEFAULT 'MEDIUM' NOT NULL,
	"due_date" timestamp with time zone,
	"completed_at" timestamp with time zone,
	"notes" text DEFAULT '' NOT NULL,
	"reason" text,
	"metadata" jsonb,
	"rejected_at" timestamp with time zone,
	"cancelled_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "tasks" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "user_districts" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"district_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "user_districts" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "user_notification_preferences" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"user_id" uuid NOT NULL,
	"notification_type" "notification_type" NOT NULL,
	"channel" "notification_channel" NOT NULL,
	"is_enabled" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "user_notification_type_channel_unique" UNIQUE("user_id","notification_type","channel")
);
--> statement-breakpoint
ALTER TABLE "user_notification_preferences" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "user_roles" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"user_id" uuid NOT NULL,
	"role_id" uuid NOT NULL,
	"role_name" "role" NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "user_roles" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "user_schools" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"user_id" uuid,
	"school_id" uuid,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid PRIMARY KEY NOT NULL,
	"first_name" varchar(255) NOT NULL,
	"middle_name" varchar(255),
	"last_name" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"full_name" varchar(255) GENERATED ALWAYS AS (concat_names("users"."first_name", "users"."middle_name", "users"."last_name")) STORED NOT NULL,
	"avatar" text,
	"is_onboarded" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "users" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "workflow_step_tasks" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"workflow_step_id" uuid NOT NULL,
	"task_type" "task_type" NOT NULL,
	"is_required" boolean DEFAULT true NOT NULL,
	"order_index" integer DEFAULT 0 NOT NULL,
	"estimated_timeline" varchar(255),
	"auto_assign_role" "role",
	"metadata" jsonb DEFAULT '{}',
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "workflow_steps" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"workflow_id" uuid NOT NULL,
	"step_number" integer NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"estimated_days" integer,
	"is_optional" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "workflows" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v7() NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"version" varchar(50) DEFAULT '1.0' NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "assessment_sessions" ADD CONSTRAINT "assessment_sessions_student_id_students_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."students"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assessment_sessions" ADD CONSTRAINT "assessment_sessions_case_id_cases_id_fk" FOREIGN KEY ("case_id") REFERENCES "public"."cases"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assessment_sessions" ADD CONSTRAINT "assessment_sessions_psychologist_id_users_id_fk" FOREIGN KEY ("psychologist_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "index_scores" ADD CONSTRAINT "index_scores_administration_id_test_administrations_id_fk" FOREIGN KEY ("administration_id") REFERENCES "public"."test_administrations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "index_scores" ADD CONSTRAINT "index_scores_index_id_test_indices_id_fk" FOREIGN KEY ("index_id") REFERENCES "public"."test_indices"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "index_subtest_mappings" ADD CONSTRAINT "index_subtest_mappings_index_id_test_indices_id_fk" FOREIGN KEY ("index_id") REFERENCES "public"."test_indices"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "index_subtest_mappings" ADD CONSTRAINT "index_subtest_mappings_subtest_id_subtests_id_fk" FOREIGN KEY ("subtest_id") REFERENCES "public"."subtests"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "subtest_scores" ADD CONSTRAINT "subtest_scores_administration_id_test_administrations_id_fk" FOREIGN KEY ("administration_id") REFERENCES "public"."test_administrations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "subtest_scores" ADD CONSTRAINT "subtest_scores_subtest_id_subtests_id_fk" FOREIGN KEY ("subtest_id") REFERENCES "public"."subtests"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "subtests" ADD CONSTRAINT "subtests_battery_id_test_batteries_id_fk" FOREIGN KEY ("battery_id") REFERENCES "public"."test_batteries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "test_administrations" ADD CONSTRAINT "test_administrations_session_id_assessment_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "public"."assessment_sessions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "test_administrations" ADD CONSTRAINT "test_administrations_battery_id_test_batteries_id_fk" FOREIGN KEY ("battery_id") REFERENCES "public"."test_batteries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "test_indices" ADD CONSTRAINT "test_indices_battery_id_test_batteries_id_fk" FOREIGN KEY ("battery_id") REFERENCES "public"."test_batteries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "availabilities" ADD CONSTRAINT "availabilities_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "case_assignments" ADD CONSTRAINT "case_assignments_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "case_assignments" ADD CONSTRAINT "case_assignments_case_id_cases_id_fk" FOREIGN KEY ("case_id") REFERENCES "public"."cases"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "case_assignments" ADD CONSTRAINT "case_assignments_deleted_by_users_id_fk" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "case_details" ADD CONSTRAINT "case_details_case_id_cases_id_fk" FOREIGN KEY ("case_id") REFERENCES "public"."cases"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "case_details" ADD CONSTRAINT "case_details_deleted_by_users_id_fk" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "case_workflow_step_statuses" ADD CONSTRAINT "case_workflow_step_statuses_case_workflow_id_case_workflows_id_fk" FOREIGN KEY ("case_workflow_id") REFERENCES "public"."case_workflows"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "case_workflow_step_statuses" ADD CONSTRAINT "case_workflow_step_statuses_step_id_workflow_steps_id_fk" FOREIGN KEY ("step_id") REFERENCES "public"."workflow_steps"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "case_workflows" ADD CONSTRAINT "case_workflows_case_id_cases_id_fk" FOREIGN KEY ("case_id") REFERENCES "public"."cases"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "case_workflows" ADD CONSTRAINT "case_workflows_workflow_id_workflows_id_fk" FOREIGN KEY ("workflow_id") REFERENCES "public"."workflows"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cases" ADD CONSTRAINT "cases_student_id_students_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."students"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cases" ADD CONSTRAINT "cases_deleted_by_users_id_fk" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "district_availabilities" ADD CONSTRAINT "district_availabilities_district_id_districts_id_fk" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "district_availabilities" ADD CONSTRAINT "district_availabilities_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "district_blocked_dates" ADD CONSTRAINT "district_blocked_dates_district_id_districts_id_fk" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "district_blocked_dates" ADD CONSTRAINT "district_blocked_dates_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "district_preferences" ADD CONSTRAINT "district_preferences_district_id_districts_id_fk" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "district_preferences" ADD CONSTRAINT "district_preferences_last_modified_by_users_id_fk" FOREIGN KEY ("last_modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "districts" ADD CONSTRAINT "districts_address_id_addresses_id_fk" FOREIGN KEY ("address_id") REFERENCES "public"."addresses"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "documents" ADD CONSTRAINT "documents_student_id_students_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."students"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "documents" ADD CONSTRAINT "documents_uploaded_user_id_users_id_fk" FOREIGN KEY ("uploaded_user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "feedback_files" ADD CONSTRAINT "feedback_files_feedback_id_feedback_id_fk" FOREIGN KEY ("feedback_id") REFERENCES "public"."feedback"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invitation_schools" ADD CONSTRAINT "invitation_schools_invitation_id_invitations_id_fk" FOREIGN KEY ("invitation_id") REFERENCES "public"."invitations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invitation_schools" ADD CONSTRAINT "invitation_schools_school_id_schools_id_fk" FOREIGN KEY ("school_id") REFERENCES "public"."schools"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_district_id_districts_id_fk" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_inviter_id_users_id_fk" FOREIGN KEY ("inviter_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_invited_by_id_users_id_fk" FOREIGN KEY ("invited_by_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "parents" ADD CONSTRAINT "parents_deleted_by_users_id_fk" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "plans" ADD CONSTRAINT "plans_student_id_students_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."students"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "plans" ADD CONSTRAINT "plans_case_id_cases_id_fk" FOREIGN KEY ("case_id") REFERENCES "public"."cases"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "plans" ADD CONSTRAINT "plans_deleted_by_users_id_fk" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_permission_id_permissions_id_fk" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "schools" ADD CONSTRAINT "schools_district_id_districts_id_fk" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "schools" ADD CONSTRAINT "schools_address_id_addresses_id_fk" FOREIGN KEY ("address_id") REFERENCES "public"."addresses"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "workflow_step_dependencies" ADD CONSTRAINT "workflow_step_dependencies_step_id_workflow_steps_id_fk" FOREIGN KEY ("step_id") REFERENCES "public"."workflow_steps"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "workflow_step_dependencies" ADD CONSTRAINT "workflow_step_dependencies_depends_on_step_id_workflow_steps_id_fk" FOREIGN KEY ("depends_on_step_id") REFERENCES "public"."workflow_steps"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_enrollments" ADD CONSTRAINT "student_enrollments_student_id_students_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."students"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_enrollments" ADD CONSTRAINT "student_enrollments_school_id_schools_id_fk" FOREIGN KEY ("school_id") REFERENCES "public"."schools"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_enrollments" ADD CONSTRAINT "student_enrollments_district_id_districts_id_fk" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_languages" ADD CONSTRAINT "student_languages_student_id_students_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."students"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_languages" ADD CONSTRAINT "student_languages_language_id_languages_id_fk" FOREIGN KEY ("language_id") REFERENCES "public"."languages"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_parents" ADD CONSTRAINT "student_parents_parent_id_parents_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."parents"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_parents" ADD CONSTRAINT "student_parents_student_id_students_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."students"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "students" ADD CONSTRAINT "students_primary_school_id_schools_id_fk" FOREIGN KEY ("primary_school_id") REFERENCES "public"."schools"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "students" ADD CONSTRAINT "students_deleted_by_users_id_fk" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_dependencies" ADD CONSTRAINT "task_dependencies_predecessor_task_id_tasks_id_fk" FOREIGN KEY ("predecessor_task_id") REFERENCES "public"."tasks"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_dependencies" ADD CONSTRAINT "task_dependencies_successor_task_id_tasks_id_fk" FOREIGN KEY ("successor_task_id") REFERENCES "public"."tasks"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_history" ADD CONSTRAINT "task_history_task_id_tasks_id_fk" FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_history" ADD CONSTRAINT "task_history_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_case_id_cases_id_fk" FOREIGN KEY ("case_id") REFERENCES "public"."cases"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_student_id_students_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."students"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_school_id_schools_id_fk" FOREIGN KEY ("school_id") REFERENCES "public"."schools"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_district_id_districts_id_fk" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_workflow_step_id_workflow_steps_id_fk" FOREIGN KEY ("workflow_step_id") REFERENCES "public"."workflow_steps"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_workflow_step_task_id_workflow_step_tasks_id_fk" FOREIGN KEY ("workflow_step_task_id") REFERENCES "public"."workflow_step_tasks"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_assigned_to_id_users_id_fk" FOREIGN KEY ("assigned_to_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_assigned_by_id_users_id_fk" FOREIGN KEY ("assigned_by_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_districts" ADD CONSTRAINT "user_districts_district_id_districts_id_fk" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_districts" ADD CONSTRAINT "user_districts_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_notification_preferences" ADD CONSTRAINT "user_notification_preferences_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_schools" ADD CONSTRAINT "user_schools_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_schools" ADD CONSTRAINT "user_schools_school_id_schools_id_fk" FOREIGN KEY ("school_id") REFERENCES "public"."schools"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_id_users_id_fk" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "workflow_step_tasks" ADD CONSTRAINT "workflow_step_tasks_workflow_step_id_workflow_steps_id_fk" FOREIGN KEY ("workflow_step_id") REFERENCES "public"."workflow_steps"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "workflow_steps" ADD CONSTRAINT "workflow_steps_workflow_id_workflows_id_fk" FOREIGN KEY ("workflow_id") REFERENCES "public"."workflows"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "session_display_id_idx" ON "assessment_sessions" USING btree ("display_id");--> statement-breakpoint
CREATE INDEX "session_student_idx" ON "assessment_sessions" USING btree ("student_id");--> statement-breakpoint
CREATE INDEX "session_psychologist_idx" ON "assessment_sessions" USING btree ("psychologist_id");--> statement-breakpoint
CREATE INDEX "session_case_idx" ON "assessment_sessions" USING btree ("case_id");--> statement-breakpoint
CREATE INDEX "session_date_idx" ON "assessment_sessions" USING btree ("session_date");--> statement-breakpoint
CREATE INDEX "session_status_idx" ON "assessment_sessions" USING btree ("session_status");--> statement-breakpoint
CREATE INDEX "session_type_idx" ON "assessment_sessions" USING btree ("session_type");--> statement-breakpoint
CREATE UNIQUE INDEX "index_score_admin_index_unique" ON "index_scores" USING btree ("administration_id","index_id");--> statement-breakpoint
CREATE INDEX "index_score_admin_idx" ON "index_scores" USING btree ("administration_id");--> statement-breakpoint
CREATE INDEX "index_score_index_idx" ON "index_scores" USING btree ("index_id");--> statement-breakpoint
CREATE INDEX "index_score_composite_idx" ON "index_scores" USING btree ("composite_score");--> statement-breakpoint
CREATE INDEX "index_score_percentile_idx" ON "index_scores" USING btree ("percentile_rank");--> statement-breakpoint
CREATE INDEX "index_score_valid_idx" ON "index_scores" USING btree ("is_valid");--> statement-breakpoint
CREATE UNIQUE INDEX "mapping_index_subtest_unique" ON "index_subtest_mappings" USING btree ("index_id","subtest_id");--> statement-breakpoint
CREATE INDEX "mapping_index_idx" ON "index_subtest_mappings" USING btree ("index_id");--> statement-breakpoint
CREATE INDEX "mapping_subtest_idx" ON "index_subtest_mappings" USING btree ("subtest_id");--> statement-breakpoint
CREATE UNIQUE INDEX "score_admin_subtest_unique" ON "subtest_scores" USING btree ("administration_id","subtest_id");--> statement-breakpoint
CREATE INDEX "score_admin_idx" ON "subtest_scores" USING btree ("administration_id");--> statement-breakpoint
CREATE INDEX "score_subtest_idx" ON "subtest_scores" USING btree ("subtest_id");--> statement-breakpoint
CREATE INDEX "score_scaled_idx" ON "subtest_scores" USING btree ("scaled_score");--> statement-breakpoint
CREATE INDEX "score_percentile_idx" ON "subtest_scores" USING btree ("percentile_rank");--> statement-breakpoint
CREATE INDEX "score_valid_idx" ON "subtest_scores" USING btree ("is_valid");--> statement-breakpoint
CREATE UNIQUE INDEX "subtest_battery_code_unique" ON "subtests" USING btree ("battery_id","code");--> statement-breakpoint
CREATE INDEX "subtest_battery_idx" ON "subtests" USING btree ("battery_id");--> statement-breakpoint
CREATE INDEX "subtest_type_idx" ON "subtests" USING btree ("subtest_type");--> statement-breakpoint
CREATE INDEX "subtest_sort_idx" ON "subtests" USING btree ("sort_order");--> statement-breakpoint
CREATE INDEX "subtest_active_idx" ON "subtests" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "admin_session_idx" ON "test_administrations" USING btree ("session_id");--> statement-breakpoint
CREATE INDEX "admin_battery_idx" ON "test_administrations" USING btree ("battery_id");--> statement-breakpoint
CREATE INDEX "admin_order_idx" ON "test_administrations" USING btree ("administration_order");--> statement-breakpoint
CREATE INDEX "admin_status_idx" ON "test_administrations" USING btree ("admin_status");--> statement-breakpoint
CREATE INDEX "admin_start_time_idx" ON "test_administrations" USING btree ("start_time");--> statement-breakpoint
CREATE INDEX "battery_category_idx" ON "test_batteries" USING btree ("category");--> statement-breakpoint
CREATE INDEX "battery_age_range_idx" ON "test_batteries" USING btree ("age_range_min","age_range_max");--> statement-breakpoint
CREATE INDEX "battery_active_idx" ON "test_batteries" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "battery_code_idx" ON "test_batteries" USING btree ("code");--> statement-breakpoint
CREATE UNIQUE INDEX "index_battery_code_unique" ON "test_indices" USING btree ("battery_id","code");--> statement-breakpoint
CREATE INDEX "index_battery_idx" ON "test_indices" USING btree ("battery_id");--> statement-breakpoint
CREATE INDEX "index_type_idx" ON "test_indices" USING btree ("index_type");--> statement-breakpoint
CREATE INDEX "index_sort_idx" ON "test_indices" USING btree ("sort_order");--> statement-breakpoint
CREATE INDEX "index_active_idx" ON "test_indices" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "address_student_idx" ON "addresses" USING btree ("student_id");--> statement-breakpoint
CREATE INDEX "address_district_idx" ON "addresses" USING btree ("district_id");--> statement-breakpoint
CREATE INDEX "address_school_idx" ON "addresses" USING btree ("school_id");--> statement-breakpoint
CREATE INDEX "address_parent_idx" ON "addresses" USING btree ("parent_id");--> statement-breakpoint
CREATE INDEX "address_zipcode_idx" ON "addresses" USING btree ("zipcode");--> statement-breakpoint
CREATE INDEX "address_type_idx" ON "addresses" USING btree ("type");--> statement-breakpoint
CREATE INDEX "address_student_not_null_idx" ON "addresses" USING btree ("student_id") WHERE student_id IS NOT NULL;--> statement-breakpoint
CREATE INDEX "address_district_not_null_idx" ON "addresses" USING btree ("district_id") WHERE district_id IS NOT NULL;--> statement-breakpoint
CREATE INDEX "address_school_not_null_idx" ON "addresses" USING btree ("school_id") WHERE school_id IS NOT NULL;--> statement-breakpoint
CREATE INDEX "address_parent_not_null_idx" ON "addresses" USING btree ("parent_id") WHERE parent_id IS NOT NULL;--> statement-breakpoint
CREATE INDEX "availability_user_idx" ON "availabilities" USING btree ("user_id");--> statement-breakpoint
CREATE UNIQUE INDEX "case_assignment_unique" ON "case_assignments" USING btree ("user_id","case_id");--> statement-breakpoint
CREATE INDEX "case_assignment_deleted_idx" ON "case_assignments" USING btree ("is_deleted");--> statement-breakpoint
CREATE INDEX "case_assignment_user_deleted_idx" ON "case_assignments" USING btree ("user_id","is_deleted");--> statement-breakpoint
CREATE INDEX "case_assignment_case_idx" ON "case_assignments" USING btree ("case_id");--> statement-breakpoint
CREATE INDEX "case_assignment_user_case_deleted_idx" ON "case_assignments" USING btree ("user_id","case_id","is_deleted");--> statement-breakpoint
CREATE INDEX "case_detail_key_idx" ON "case_details" USING btree ("key");--> statement-breakpoint
CREATE INDEX "case_detail_deleted_idx" ON "case_details" USING btree ("is_deleted");--> statement-breakpoint
CREATE UNIQUE INDEX "case_step_status_unique" ON "case_workflow_step_statuses" USING btree ("case_workflow_id","step_id");--> statement-breakpoint
CREATE INDEX "case_step_status_workflow_idx" ON "case_workflow_step_statuses" USING btree ("case_workflow_id");--> statement-breakpoint
CREATE INDEX "case_step_status_status_idx" ON "case_workflow_step_statuses" USING btree ("status");--> statement-breakpoint
CREATE UNIQUE INDEX "case_workflows_unique" ON "case_workflows" USING btree ("case_id");--> statement-breakpoint
CREATE INDEX "case_workflows_status_idx" ON "case_workflows" USING btree ("status");--> statement-breakpoint
CREATE INDEX "case_display_id_idx" ON "cases" USING btree ("display_id");--> statement-breakpoint
CREATE INDEX "case_priority_status_idx" ON "cases" USING btree ("priority","status");--> statement-breakpoint
CREATE INDEX "case_student_idx" ON "cases" USING btree ("student_id");--> statement-breakpoint
CREATE INDEX "case_type_idx" ON "cases" USING btree ("case_type");--> statement-breakpoint
CREATE INDEX "case_active_idx" ON "cases" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "case_deleted_idx" ON "cases" USING btree ("is_deleted");--> statement-breakpoint
CREATE INDEX "case_eval_due_date_idx" ON "cases" USING btree ("evaluation_due_date");--> statement-breakpoint
CREATE INDEX "case_student_deleted_idx" ON "cases" USING btree ("student_id","is_deleted");--> statement-breakpoint
CREATE INDEX "district_avail_district_idx" ON "district_availabilities" USING btree ("district_id");--> statement-breakpoint
CREATE INDEX "district_avail_day_idx" ON "district_availabilities" USING btree ("day");--> statement-breakpoint
CREATE INDEX "district_avail_type_idx" ON "district_availabilities" USING btree ("type");--> statement-breakpoint
CREATE INDEX "district_avail_active_idx" ON "district_availabilities" USING btree ("is_active");--> statement-breakpoint
CREATE UNIQUE INDEX "district_avail_unique" ON "district_availabilities" USING btree ("district_id","type","day","start_time","end_time");--> statement-breakpoint
CREATE INDEX "district_blocked_district_idx" ON "district_blocked_dates" USING btree ("district_id");--> statement-breakpoint
CREATE INDEX "district_blocked_dates_idx" ON "district_blocked_dates" USING btree ("start_date","end_date");--> statement-breakpoint
CREATE INDEX "district_blocked_type_idx" ON "district_blocked_dates" USING btree ("block_type");--> statement-breakpoint
CREATE INDEX "district_blocked_active_idx" ON "district_blocked_dates" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "district_blocked_recurring_idx" ON "district_blocked_dates" USING btree ("is_recurring");--> statement-breakpoint
CREATE UNIQUE INDEX "district_preference_unique" ON "district_preferences" USING btree ("district_id","key");--> statement-breakpoint
CREATE INDEX "district_pref_district_idx" ON "district_preferences" USING btree ("district_id");--> statement-breakpoint
CREATE INDEX "district_pref_category_idx" ON "district_preferences" USING btree ("category");--> statement-breakpoint
CREATE INDEX "district_pref_key_idx" ON "district_preferences" USING btree ("key");--> statement-breakpoint
CREATE INDEX "document_student_idx" ON "documents" USING btree ("student_id");--> statement-breakpoint
CREATE INDEX "document_category_idx" ON "documents" USING btree ("category");--> statement-breakpoint
CREATE INDEX "document_session_idx" ON "documents" USING btree ("assessment_session_id");--> statement-breakpoint
CREATE INDEX "document_test_admin_idx" ON "documents" USING btree ("test_administration_id");--> statement-breakpoint
CREATE INDEX "idx_feedback_rating" ON "feedback" USING btree ("rating");--> statement-breakpoint
CREATE INDEX "idx_feedback_type" ON "feedback" USING btree ("type");--> statement-breakpoint
CREATE INDEX "idx_feedback_status" ON "feedback" USING btree ("status");--> statement-breakpoint
CREATE INDEX "idx_feedback_issue_type" ON "feedback" USING btree ("issue_type");--> statement-breakpoint
CREATE UNIQUE INDEX "invitation_school_unique" ON "invitation_schools" USING btree ("invitation_id","school_id");--> statement-breakpoint
CREATE INDEX "invitation_schools_invitation_idx" ON "invitation_schools" USING btree ("invitation_id");--> statement-breakpoint
CREATE INDEX "invitation_schools_school_idx" ON "invitation_schools" USING btree ("school_id");--> statement-breakpoint
CREATE INDEX "invitation_status_idx" ON "invitations" USING btree ("status");--> statement-breakpoint
CREATE INDEX "invitation_email_idx" ON "invitations" USING btree ("email");--> statement-breakpoint
CREATE INDEX "invitation_district_idx" ON "invitations" USING btree ("district_id");--> statement-breakpoint
CREATE INDEX "invitation_expires_idx" ON "invitations" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "invitation_token_idx" ON "invitations" USING btree ("token");--> statement-breakpoint
CREATE INDEX "invitation_active_idx" ON "invitations" USING btree ("email","district_id","status");--> statement-breakpoint
CREATE UNIQUE INDEX "language_code_unique" ON "languages" USING btree ("code");--> statement-breakpoint
CREATE INDEX "language_name_idx" ON "languages" USING btree ("name");--> statement-breakpoint
CREATE INDEX "idx_notification_user_read" ON "notifications" USING btree ("user_id","is_read","expires_at");--> statement-breakpoint
CREATE INDEX "idx_notification_user_archived" ON "notifications" USING btree ("user_id","is_archived","expires_at");--> statement-breakpoint
CREATE INDEX "parent_name_idx" ON "parents" USING btree ("full_name");--> statement-breakpoint
CREATE INDEX "parent_email_idx" ON "parents" USING btree ("primary_email");--> statement-breakpoint
CREATE INDEX "parent_deleted_idx" ON "parents" USING btree ("is_deleted");--> statement-breakpoint
CREATE INDEX "permission_name_idx" ON "permissions" USING btree ("name");--> statement-breakpoint
CREATE INDEX "plan_display_id_idx" ON "plans" USING btree ("display_id");--> statement-breakpoint
CREATE INDEX "plan_student_idx" ON "plans" USING btree ("student_id");--> statement-breakpoint
CREATE INDEX "plan_case_idx" ON "plans" USING btree ("case_id");--> statement-breakpoint
CREATE INDEX "plan_type_status_idx" ON "plans" USING btree ("type","status");--> statement-breakpoint
CREATE INDEX "plan_expiration_idx" ON "plans" USING btree ("expiration_date");--> statement-breakpoint
CREATE INDEX "plan_deleted_idx" ON "plans" USING btree ("is_deleted");--> statement-breakpoint
CREATE INDEX "role_permission_role_idx" ON "role_permissions" USING btree ("role_id");--> statement-breakpoint
CREATE INDEX "role_permission_permission_idx" ON "role_permissions" USING btree ("permission_id");--> statement-breakpoint
CREATE INDEX "role_name_idx" ON "roles" USING btree ("name");--> statement-breakpoint
CREATE INDEX "school_district_idx" ON "schools" USING btree ("district_id");--> statement-breakpoint
CREATE UNIQUE INDEX "step_dependencies_unique" ON "workflow_step_dependencies" USING btree ("step_id","depends_on_step_id");--> statement-breakpoint
CREATE INDEX "step_dependencies_step_idx" ON "workflow_step_dependencies" USING btree ("step_id");--> statement-breakpoint
CREATE INDEX "student_enrollment_student_idx" ON "student_enrollments" USING btree ("student_id");--> statement-breakpoint
CREATE INDEX "student_enrollment_school_idx" ON "student_enrollments" USING btree ("school_id");--> statement-breakpoint
CREATE INDEX "student_enrollment_district_idx" ON "student_enrollments" USING btree ("district_id");--> statement-breakpoint
CREATE UNIQUE INDEX "student_enrollment_unique" ON "student_enrollments" USING btree ("student_id","school_id");--> statement-breakpoint
CREATE UNIQUE INDEX "student_language_unique" ON "student_languages" USING btree ("student_id","language_id");--> statement-breakpoint
CREATE INDEX "student_language_primary_idx" ON "student_languages" USING btree ("is_primary");--> statement-breakpoint
CREATE UNIQUE INDEX "student_parent_unique" ON "student_parents" USING btree ("student_id","parent_id");--> statement-breakpoint
CREATE INDEX "student_parent_primary_idx" ON "student_parents" USING btree ("is_primary_contact");--> statement-breakpoint
CREATE UNIQUE INDEX "student_id_school_unique" ON "students" USING btree ("student_id_number","primary_school_id");--> statement-breakpoint
CREATE INDEX "student_enrollment_status_idx" ON "students" USING btree ("enrollment_status");--> statement-breakpoint
CREATE INDEX "student_deleted_idx" ON "students" USING btree ("is_deleted");--> statement-breakpoint
CREATE INDEX "student_name_idx" ON "students" USING btree ("full_name");--> statement-breakpoint
CREATE INDEX "task_assigned_to_idx" ON "tasks" USING btree ("assigned_to_id");--> statement-breakpoint
CREATE INDEX "task_status_priority_idx" ON "tasks" USING btree ("status","priority");--> statement-breakpoint
CREATE INDEX "task_due_date_idx" ON "tasks" USING btree ("due_date");--> statement-breakpoint
CREATE INDEX "task_case_idx" ON "tasks" USING btree ("case_id");--> statement-breakpoint
CREATE INDEX "task_student_idx" ON "tasks" USING btree ("student_id");--> statement-breakpoint
CREATE INDEX "task_district_idx" ON "tasks" USING btree ("district_id");--> statement-breakpoint
CREATE INDEX "task_type_idx" ON "tasks" USING btree ("task_type");--> statement-breakpoint
CREATE INDEX "task_completed_idx" ON "tasks" USING btree ("completed_at");--> statement-breakpoint
CREATE INDEX "task_assigned_status_due_idx" ON "tasks" USING btree ("assigned_to_id","status","due_date");--> statement-breakpoint
CREATE INDEX "task_district_priority_idx" ON "tasks" USING btree ("district_id","priority","created_at");--> statement-breakpoint
CREATE INDEX "task_active_idx" ON "tasks" USING btree ("assigned_to_id","due_date");--> statement-breakpoint
CREATE INDEX "task_overdue_idx" ON "tasks" USING btree ("due_date","status");--> statement-breakpoint
CREATE INDEX "task_workflow_step_task_idx" ON "tasks" USING btree ("workflow_step_task_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_district_unique" ON "user_districts" USING btree ("user_id","district_id");--> statement-breakpoint
CREATE INDEX "user_district_user_idx" ON "user_districts" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "user_district_district_idx" ON "user_districts" USING btree ("district_id");--> statement-breakpoint
CREATE INDEX "user_notification_preferences_user_idx" ON "user_notification_preferences" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "user_notification_preferences_type_idx" ON "user_notification_preferences" USING btree ("notification_type");--> statement-breakpoint
CREATE INDEX "user_notification_preferences_channel_idx" ON "user_notification_preferences" USING btree ("channel");--> statement-breakpoint
CREATE UNIQUE INDEX "user_role_unique" ON "user_roles" USING btree ("user_id","role_id");--> statement-breakpoint
CREATE INDEX "user_role_role_idx" ON "user_roles" USING btree ("role_id");--> statement-breakpoint
CREATE INDEX "user_role_user_name_idx" ON "user_roles" USING btree ("user_id","role_name");--> statement-breakpoint
CREATE UNIQUE INDEX "user_school_unique" ON "user_schools" USING btree ("user_id","school_id");--> statement-breakpoint
CREATE INDEX "user_school_user_idx" ON "user_schools" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "user_school_school_idx" ON "user_schools" USING btree ("school_id");--> statement-breakpoint
CREATE INDEX "user_name_idx" ON "users" USING btree ("full_name");--> statement-breakpoint
CREATE INDEX "user_email_idx" ON "users" USING btree ("email");--> statement-breakpoint
CREATE INDEX "workflow_step_tasks_step_idx" ON "workflow_step_tasks" USING btree ("workflow_step_id");--> statement-breakpoint
CREATE INDEX "workflow_step_tasks_type_idx" ON "workflow_step_tasks" USING btree ("task_type");--> statement-breakpoint
CREATE UNIQUE INDEX "workflow_step_tasks_unique" ON "workflow_step_tasks" USING btree ("workflow_step_id","task_type","order_index");--> statement-breakpoint
CREATE UNIQUE INDEX "workflow_steps_unique" ON "workflow_steps" USING btree ("workflow_id","step_number");--> statement-breakpoint
CREATE INDEX "workflow_steps_workflow_idx" ON "workflow_steps" USING btree ("workflow_id");--> statement-breakpoint
CREATE INDEX "workflows_active_idx" ON "workflows" USING btree ("is_active");--> statement-breakpoint
CREATE POLICY "users_can_view_assessment_sessions_based_on_role" ON "assessment_sessions" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR')
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')
    )
  
				AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = assessment_sessions.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = assessment_sessions.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = assessment_sessions.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_create_assessment_sessions" ON "assessment_sessions" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')
    )
  
			AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = assessment_sessions.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = assessment_sessions.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = assessment_sessions.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
			AND assessment_sessions.psychologist_id = auth.uid()
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_update_assessment_sessions" ON "assessment_sessions" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')
    )
  
				AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = assessment_sessions.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = assessment_sessions.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = assessment_sessions.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
			)
		);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_assessment_sessions" ON "assessment_sessions" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "users_can_view_index_scores_based_on_role" ON "index_scores" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR')
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')
    )
  
				AND EXISTS (
					SELECT 1 FROM test_administrations ta
					JOIN assessment_sessions a ON a.id = ta.session_id
					WHERE ta.id = index_scores.administration_id
					AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = a.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = a.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = a.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
				)
			)
		);--> statement-breakpoint
CREATE POLICY "all_users_can_view_index_subtest_mappings" ON "index_subtest_mappings" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);--> statement-breakpoint
CREATE POLICY "users_can_view_subtest_scores_based_on_role" ON "subtest_scores" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR')
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')
    )
  
				AND EXISTS (
					SELECT 1 FROM test_administrations ta
					JOIN assessment_sessions a ON a.id = ta.session_id
					WHERE ta.id = subtest_scores.administration_id
					AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = a.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = a.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = a.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
				)
			)
		);--> statement-breakpoint
CREATE POLICY "all_users_can_view_subtests" ON "subtests" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);--> statement-breakpoint
CREATE POLICY "users_can_view_test_administrations_based_on_role" ON "test_administrations" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR')
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')
    )
  
				AND EXISTS (
					SELECT 1 FROM assessment_sessions a
					WHERE a.id = test_administrations.session_id
					AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = a.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = a.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = a.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
				)
			)
		);--> statement-breakpoint
CREATE POLICY "all_users_can_view_test_batteries" ON "test_batteries" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);--> statement-breakpoint
CREATE POLICY "all_users_can_view_test_indices" ON "test_indices" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);--> statement-breakpoint
CREATE POLICY "users_can_view_addresses_based_on_access" ON "addresses" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(district_id IS NOT NULL AND 
    EXISTS (
      SELECT 1
      FROM user_districts
      WHERE user_id = auth.uid()
      AND district_id = district_id
    )
  )
			OR
			(school_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM schools s 
				WHERE s.id = school_id 
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = auth.uid()
					AND ud.district_id = s.district_id
				)
			))
			OR
			(student_id IS NOT NULL AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  )
			OR
			(parent_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM student_parents sp
				WHERE sp.parent_id = parent_id
				AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = sp.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = sp.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = sp.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
			))
		);--> statement-breakpoint
CREATE POLICY "users_can_manage_addresses_for_accessible_entities" ON "addresses" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(district_id IS NOT NULL AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')
    )
  
        AND EXISTS (
          SELECT 1
          FROM user_districts ud
          WHERE ud.user_id = auth.uid()
          AND ud.district_id = district_id
        )
      )
    )
  )
			OR
			(school_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM schools s 
				WHERE s.id = school_id 
				AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')
    )
  
        AND EXISTS (
          SELECT 1
          FROM user_districts ud
          WHERE ud.user_id = auth.uid()
          AND ud.district_id = s.district_id
        )
      )
    )
  
			))
			OR
			(student_id IS NOT NULL AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  )
			OR
			(parent_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM student_parents sp
				WHERE sp.parent_id = parent_id
				AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = sp.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = sp.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = sp.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
			))
		);--> statement-breakpoint
CREATE POLICY "users_can_view_availabilities" ON "availabilities" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			user_id = auth.uid()
			OR
			EXISTS (
				SELECT 1 
				FROM user_districts ud_current
				JOIN user_districts ud_target ON ud_current.district_id = ud_target.district_id
				WHERE ud_current.user_id = auth.uid()
				AND ud_target.user_id = availabilities.user_id
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_manage_their_availabilities" ON "availabilities" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			user_id = auth.uid()
		);--> statement-breakpoint
CREATE POLICY "users_can_view_case_assignments_based_on_role" ON "case_assignments" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			user_id = auth.uid()
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM user_districts ud
					WHERE ud.user_id = auth.uid()
				)
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_create_case_assignments" ON "case_assignments" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM user_districts ud
					WHERE ud.user_id = auth.uid()
				)
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_update_case_assignments" ON "case_assignments" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM user_districts ud
					WHERE ud.user_id = auth.uid()
				)
			)
		);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_case_assignments" ON "case_assignments" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "users_can_view_cases_based_on_role" ON "cases" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM case_assignments ca
					WHERE ca.user_id = auth.uid()
					AND ca.case_id = cases.id
					AND ca.is_deleted = false
				)
			)
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = student_id
					AND ud.user_id = auth.uid()
				)
			)
			OR
			(
				NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = student_id
					AND us.user_id = auth.uid()
				)
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_create_cases" ON "cases" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
		
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
		OR
		(
			NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
			AND (
				(
					
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
					AND EXISTS (
						SELECT 1
						FROM student_enrollments se
						JOIN schools s ON s.id = se.school_id
						JOIN user_districts ud ON ud.district_id = s.district_id
						WHERE se.student_id = student_id
						AND ud.user_id = auth.uid()
					)
				)
				OR
				(
					NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
						AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
						AND EXISTS (
							SELECT 1
							FROM student_enrollments se
							JOIN user_schools us ON us.school_id = se.school_id
							WHERE se.student_id = student_id
							AND us.user_id = auth.uid()
						)
				)
			)
		)
	);--> statement-breakpoint
CREATE POLICY "users_can_update_accessible_cases" ON "cases" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
		
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
		OR
		(
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
			AND EXISTS (
				SELECT 1
				FROM case_assignments ca
				WHERE ca.user_id = auth.uid()
				AND ca.case_id = cases.id
				AND ca.is_deleted = false
			)
		)
		OR
		(
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
			AND EXISTS (
				SELECT 1
				FROM student_enrollments se
				JOIN schools s ON s.id = se.school_id
				JOIN user_districts ud ON ud.district_id = s.district_id
				WHERE se.student_id = student_id
				AND ud.user_id = auth.uid()
			)
		)
		OR
		(
			NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = student_id
					AND us.user_id = auth.uid()
				)
		)
	);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_cases" ON "cases" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "users_can_view_district_availabilities" ON "district_availabilities" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = auth.uid()
				AND ud.district_id = district_id
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_manage_district_availabilities" ON "district_availabilities" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')
    )
  
        AND EXISTS (
          SELECT 1
          FROM user_districts ud
          WHERE ud.user_id = auth.uid()
          AND ud.district_id = district_id
        )
      )
    )
  
		);--> statement-breakpoint
CREATE POLICY "users_can_view_district_blocked_dates" ON "district_blocked_dates" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = auth.uid()
				AND ud.district_id = district_id
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_manage_district_blocked_dates" ON "district_blocked_dates" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')
    )
  
        AND EXISTS (
          SELECT 1
          FROM user_districts ud
          WHERE ud.user_id = auth.uid()
          AND ud.district_id = district_id
        )
      )
    )
  
		);--> statement-breakpoint
CREATE POLICY "users_can_manage_district_preferences_select" ON "district_preferences" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
        OR
        EXISTS (
          SELECT 1 FROM user_districts ud 
          WHERE ud.user_id = auth.uid() 
          AND ud.district_id = "district_preferences".district_id
        )
      );--> statement-breakpoint
CREATE POLICY "users_can_manage_district_preferences_all" ON "district_preferences" AS PERMISSIVE FOR ALL TO "authenticated" USING (
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')
    )
  
        AND EXISTS (
          SELECT 1
          FROM user_districts ud
          WHERE ud.user_id = auth.uid()
          AND ud.district_id = "district_preferences".district_id
        )
      )
    )
  );--> statement-breakpoint
CREATE POLICY "all_users_can_view_districts_select" ON "districts" AS PERMISSIVE FOR SELECT TO "authenticated" USING (auth.uid() IS NOT NULL);--> statement-breakpoint
CREATE POLICY "only_superuser_can_create_districts" ON "districts" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "authorized_roles_can_update_districts" ON "districts" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')
    )
  
        AND EXISTS (
          SELECT 1
          FROM user_districts ud
          WHERE ud.user_id = auth.uid()
          AND ud.district_id = id
        )
      )
    )
  );--> statement-breakpoint
CREATE POLICY "only_superusers_can_delete_districts" ON "districts" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "users_can_view_documents_for_accessible_students" ON "documents" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
		);--> statement-breakpoint
CREATE POLICY "users_can_upload_documents_for_accessible_students" ON "documents" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
			auth.uid() IS NOT NULL
			AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
		);--> statement-breakpoint
CREATE POLICY "users_can_update_their_documents" ON "documents" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			uploaded_user_id = auth.uid()
		);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_documents" ON "documents" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "Anyone can get feedback file data" ON "feedback_files" AS PERMISSIVE FOR SELECT TO public USING (true);--> statement-breakpoint
CREATE POLICY "Only authenticated user can create feedback file data" ON "feedback_files" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (EXISTS (
      SELECT 1 FROM feedback f
      WHERE f.id = feedback_files.feedback_id AND f.user_id = auth.uid()
    ));--> statement-breakpoint
CREATE POLICY "Users can delete their own feedback files or super users can delete any" ON "feedback_files" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 FROM feedback f
				WHERE f.id = feedback_files.feedback_id AND f.user_id = auth.uid()
			));--> statement-breakpoint
CREATE POLICY "Anyone can get feedback data" ON "feedback" AS PERMISSIVE FOR SELECT TO public USING (true);--> statement-breakpoint
CREATE POLICY "Only authenticated user can create feedback data" ON "feedback" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (user_id = auth.uid());--> statement-breakpoint
CREATE POLICY "Users can update their own feedback or super users can update any" ON "feedback" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (user_id = auth.uid() OR 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "Users can delete their own feedback or super users can delete any" ON "feedback" AS PERMISSIVE FOR DELETE TO "authenticated" USING (user_id = auth.uid() OR 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "users_can_view_invitation_schools" ON "invitation_schools" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 FROM invitations i
				WHERE i.id = invitation_id
				AND (
					i.email = auth.email()
					OR
					(
						
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')
    )
  
						AND EXISTS (
							SELECT 1 FROM user_districts ud
							WHERE ud.user_id = auth.uid()
							AND ud.district_id = i.district_id
						)
					)
				)
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_manage_invitation_schools" ON "invitation_schools" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 FROM invitations i
				WHERE i.id = invitation_id
				AND 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')
    )
  
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = auth.uid()
						AND ud.district_id = i.district_id
				)
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_view_invitations" ON "invitations" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			email = auth.email()
			OR
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')
    )
  
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = auth.uid()
					AND ud.district_id = district_id
				)
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_create_invitations" ON "invitations" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')
    )
  
			AND EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = auth.uid()
				AND ud.district_id = district_id
			)
			AND inviter_id = auth.uid()
		);--> statement-breakpoint
CREATE POLICY "users_can_update_relevant_invitations" ON "invitations" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
			email = auth.email()
			OR
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')
    )
  
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = auth.uid()
					AND ud.district_id = district_id
				)
			)
		);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_invitations" ON "invitations" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "users_can_view_join_requests" ON "join_requests" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
		
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
	);--> statement-breakpoint
CREATE POLICY "users_can_manage_join_requests" ON "join_requests" AS PERMISSIVE FOR INSERT TO "anon" WITH CHECK (
		true
	);--> statement-breakpoint
CREATE POLICY "superuser_can_manage_join_requests" ON "join_requests" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
		);--> statement-breakpoint
CREATE POLICY "all_users_can_view_languages" ON "languages" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);--> statement-breakpoint
CREATE POLICY "only_authenticated_users_can_get_their_own_notifications" ON "notifications" AS PERMISSIVE FOR SELECT TO "authenticated" USING (auth.uid() = user_id);--> statement-breakpoint
CREATE POLICY "only_authenticated_users_can_update_their_own_notifications" ON "notifications" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (auth.uid() = user_id);--> statement-breakpoint
CREATE POLICY "only_authenticated_users_can_delete_their_own_notifications" ON "notifications" AS PERMISSIVE FOR DELETE TO "authenticated" USING (auth.uid() = user_id);--> statement-breakpoint
CREATE POLICY "users_can_view_parents_based_on_student_access" ON "parents" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 FROM student_parents sp
				WHERE sp.parent_id = parents.id
				AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = sp.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = sp.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = sp.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_create_parents_for_accessible_students" ON "parents" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (auth.uid() IS NOT NULL);--> statement-breakpoint
CREATE POLICY "users_can_update_parents_for_accessible_students" ON "parents" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 FROM student_parents sp
				WHERE sp.parent_id = parents.id
				AND 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = sp.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = sp.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = sp.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
			)
		);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_parents" ON "parents" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "all_users_can_view_permissions_select" ON "permissions" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);--> statement-breakpoint
CREATE POLICY "users_can_view_plans_for_accessible_cases" ON "plans" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM case_assignments ca
					WHERE ca.user_id = auth.uid()
					AND ca.case_id = plans.case_id
					AND ca.is_deleted = false
				)
			)
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = plans.student_id
					AND ud.user_id = auth.uid()
				)
			)
			OR
			(
				NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = plans.student_id
					AND us.user_id = auth.uid()
				)
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_manage_plans_for_accessible_cases" ON "plans" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM case_assignments ca
					WHERE ca.user_id = auth.uid()
					AND ca.case_id = plans.case_id
					AND ca.is_deleted = false
				)
			)
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = plans.student_id
					AND ud.user_id = auth.uid()
				)
			)
			OR
			(
				NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = plans.student_id
					AND us.user_id = auth.uid()
				)
			)
		);--> statement-breakpoint
CREATE POLICY "all_users_can_view_role_permissions_select" ON "role_permissions" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);--> statement-breakpoint
CREATE POLICY "all_users_can_view_roles_select" ON "roles" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);--> statement-breakpoint
CREATE POLICY "users_can_view_schools_in_their_districts" ON "schools" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = auth.uid()
				AND ud.district_id = district_id
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_create_schools" ON "schools" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')
    )
  
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = auth.uid()
					AND ud.district_id = district_id
				)
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_update_schools" ON "schools" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')
    )
  
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = auth.uid()
					AND ud.district_id = district_id
				)
			)
		);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_schools" ON "schools" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "users_can_view_enrollments_for_accessible_students" ON "student_enrollments" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1
				FROM user_districts ud
				JOIN schools s ON s.district_id = ud.district_id
				WHERE s.id = student_enrollments.school_id
				AND ud.user_id = auth.uid()
			)
			OR
			EXISTS (
				SELECT 1
				FROM user_schools us
				WHERE us.school_id = student_enrollments.school_id
				AND us.user_id = auth.uid()
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_manage_enrollments_for_accessible_students" ON "student_enrollments" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND (
					EXISTS (
						SELECT 1
						FROM user_districts ud
						JOIN schools s ON s.district_id = ud.district_id
						WHERE s.id = student_enrollments.school_id
						AND ud.user_id = auth.uid()
					)
					OR
					EXISTS (
						SELECT 1
						FROM user_schools us
						WHERE us.school_id = student_enrollments.school_id
						AND us.user_id = auth.uid()
					)
				)
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_view_student_languages" ON "student_languages" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
		);--> statement-breakpoint
CREATE POLICY "users_can_manage_student_languages" ON "student_languages" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
				AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_view_student_parent_relationships" ON "student_parents" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = student_parents.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = student_parents.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = student_parents.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
		);--> statement-breakpoint
CREATE POLICY "users_can_manage_student_parent_relationships" ON "student_parents" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = student_parents.student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = student_parents.student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = student_parents.student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
				AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_view_students_based_on_role" ON "students" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = students.id
					AND ud.user_id = auth.uid()
				)
			)
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM case_assignments ca
					JOIN cases c ON c.id = ca.case_id
					WHERE c.student_id = students.id
					AND ca.user_id = auth.uid()
					AND ca.is_deleted = false
					AND c.is_deleted = false
				)
			)
			OR
			(
				 NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = students.id
					AND us.user_id = auth.uid()
				)
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_create_students" ON "students" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND EXISTS (
					SELECT 1 FROM schools s
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE s.id = primary_school_id
					AND ud.user_id = auth.uid()
				)
			)
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('ASSISTANT')
    )
  
				AND EXISTS (
						SELECT 1 FROM user_schools us
						WHERE us.user_id = auth.uid()
						AND us.school_id = primary_school_id
					)
			)
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_update_students" ON "students" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
				AND EXISTS (
					SELECT 1 FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = students.id
					AND ud.user_id = auth.uid()
				)
			)
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('ASSISTANT', 'SCHOOL_COORDINATOR', 'SCHOOL_ADMIN')
    )
  
				AND EXISTS (
					SELECT 1 FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = students.id
					AND us.user_id = auth.uid()
				)
			)
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST')
    )
  
				AND EXISTS (
					SELECT 1 FROM cases c
					JOIN case_assignments ca ON ca.case_id = c.id
					WHERE c.student_id = students.id
					AND ca.user_id = auth.uid()
					AND ca.is_deleted = false
					AND c.is_deleted = false
				)
			)
		);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_students" ON "students" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "users_can_view_tasks_in_their_scope" ON "tasks" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			assigned_to_id = auth.uid()
			OR
			assigned_by_id = auth.uid()
			OR
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = auth.uid()
				AND ud.district_id = district_id
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_create_tasks_in_their_scope" ON "tasks" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
			auth.uid() IS NOT NULL
			AND EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = auth.uid()
				AND ud.district_id = district_id
			)
			AND (
				case_id IS NULL
				OR (
					
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
					OR
					(
						
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
						AND EXISTS (
							SELECT 1
							FROM case_assignments ca
							WHERE ca.user_id = auth.uid()
							AND ca.case_id = case_id
							AND ca.is_deleted = false
						)
					)
					OR
					(
						EXISTS (
							SELECT 1
							FROM user_districts ud
							WHERE ud.user_id = auth.uid()
							AND ud.district_id = district_id
						)
					)
				)
			)
			AND (
				student_id IS NULL
				OR 
    (
      
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = student_id
          AND ud.user_id = auth.uid()
        )
      )
      OR
      (
        
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = student_id
          AND ca.user_id = auth.uid()
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  
        AND NOT 
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = student_id
          AND us.user_id = auth.uid()
        )
      )
    )
  
			)
		);--> statement-breakpoint
CREATE POLICY "users_can_update_assigned_tasks" ON "tasks" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
			assigned_to_id = auth.uid()
			OR
			assigned_by_id = auth.uid()
			OR
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
		);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_tasks" ON "tasks" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );--> statement-breakpoint
CREATE POLICY "users_can_view_district_associations" ON "user_districts" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			user_districts.user_id = auth.uid()
		);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_manage_district_associations" ON "user_districts" AS PERMISSIVE FOR ALL TO "authenticated" USING (
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			(
				
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')
    )
  
				AND user_districts.user_id = auth.uid()
			)
		);--> statement-breakpoint
CREATE POLICY "only_authenticated_users_can_get_their_own_notification_preferences" ON "user_notification_preferences" AS PERMISSIVE FOR SELECT TO "authenticated" USING (auth.uid() = user_id);--> statement-breakpoint
CREATE POLICY "only_authenticated_users_can_update_their_own_notification_preferences" ON "user_notification_preferences" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (auth.uid() = user_id);--> statement-breakpoint
CREATE POLICY "all_users_can_view_user_roles_select" ON "user_roles" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);--> statement-breakpoint
CREATE POLICY "all_users_can_manage_user_roles_all" ON "user_roles" AS PERMISSIVE FOR ALL TO "authenticated" USING (auth.uid() IS NOT NULL);--> statement-breakpoint
CREATE POLICY "users_can_view_users_in_their_scope" ON "users" AS PERMISSIVE FOR SELECT TO "authenticated" USING (
			id = auth.uid()
			OR
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
			OR
			EXISTS (
				SELECT 1 
				FROM user_districts ud_current
				JOIN user_districts ud_target ON ud_current.district_id = ud_target.district_id
				WHERE ud_current.user_id = auth.uid()
				AND ud_target.user_id = users.id
			)
		);--> statement-breakpoint
CREATE POLICY "users_cannot_be_created_directly" ON "users" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (false);--> statement-breakpoint
CREATE POLICY "authorized_roles_can_update_users" ON "users" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (
		id = auth.uid()
		OR
		
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  
		OR
		(
			
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')
    )
  
			AND EXISTS (
				SELECT 1 
				FROM user_districts ud_current
				JOIN user_districts ud_target ON ud_current.district_id = ud_target.district_id
				WHERE ud_current.user_id = auth.uid()
				AND ud_target.user_id = users.id
			)
		)
	);--> statement-breakpoint
CREATE POLICY "only_superuser_can_delete_users" ON "users" AS PERMISSIVE FOR DELETE TO "authenticated" USING (
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = auth.uid()
      AND role_name = 'SUPER_USER'
    )
  );